#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Get project name
get_project_name() {
    if [ -z "$1" ]; then
        read -p "Enter project name (default: my-app): " PROJECT_NAME
        PROJECT_NAME=${PROJECT_NAME:-my-app}
    else
        PROJECT_NAME=$1
    fi
    
    if [ -d "$PROJECT_NAME" ]; then
        print_error "Directory '$PROJECT_NAME' already exists!"
        exit 1
    fi
}

# Create Next.js app
create_nextjs_app() {
    print_status "Creating Next.js application..."
    npx create-next-app@latest "$PROJECT_NAME" --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
    
    if [ $? -ne 0 ]; then
        print_error "Failed to create Next.js app"
        exit 1
    fi
    
    cd "$PROJECT_NAME"
    print_success "Next.js app created successfully"
}

# Install additional dependencies
install_dependencies() {
    print_status "Installing Clerk and Convex..."
    
    # Install Clerk
    npm install @clerk/nextjs
    
    # Install Convex
    npm install convex
    
    # Install additional useful packages
    npm install @types/node @types/react @types/react-dom
    
    print_success "Dependencies installed successfully"
}

# Setup Convex
setup_convex() {
    print_status "Setting up Convex..."
    
    # Initialize Convex
    npx convex dev --once --configure=new
    
    # Create convex directory and basic files
    mkdir -p convex
    
    # Create schema.ts
    cat > convex/schema.ts << 'EOF'
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Example table - replace with your own schema
  messages: defineTable({
    author: v.string(),
    body: v.string(),
  }),
});
EOF

    # Create example function
    cat > convex/messages.ts << 'EOF'
import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const get = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("messages").collect();
  },
});

export const send = mutation({
  args: { body: v.string(), author: v.string() },
  handler: async (ctx, args) => {
    await ctx.db.insert("messages", { body: args.body, author: args.author });
  },
});
EOF

    print_success "Convex setup completed"
}

# Setup Clerk
setup_clerk() {
    print_status "Setting up Clerk..."
    
    # Create .env.local with Clerk placeholders
    cat > .env.local << 'EOF'
# Clerk Keys
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key_here
CLERK_SECRET_KEY=your_clerk_secret_key_here

# Clerk URLs (optional)
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# Convex
CONVEX_DEPLOYMENT=your_convex_deployment_url_here
NEXT_PUBLIC_CONVEX_URL=your_convex_url_here
EOF

    # Update middleware.ts
    cat > src/middleware.ts << 'EOF'
import { authMiddleware } from "@clerk/nextjs";

export default authMiddleware({
  // Routes that can be accessed while signed out
  publicRoutes: ["/"],
  // Routes that can always be accessed, and have
  // no authentication information
  ignoredRoutes: ["/api/webhook/clerk"]
});

export const config = {
  // Protects all routes, including api/trpc.
  // See https://clerk.com/docs/references/nextjs/auth-middleware
  // for more information about configuring your Middleware
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
EOF

    # Create Clerk provider component
    mkdir -p src/components/providers
    cat > src/components/providers/clerk-provider.tsx << 'EOF'
"use client";

import { ClerkProvider } from "@clerk/nextjs";

export function ClerkClientProvider({ children }: { children: React.ReactNode }) {
  return <ClerkProvider>{children}</ClerkProvider>;
}
EOF

    # Create Convex provider
    cat > src/components/providers/convex-provider.tsx << 'EOF'
"use client";

import { ConvexProvider, ConvexReactClient } from "convex/react";
import { ReactNode } from "react";

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export function ConvexClientProvider({ children }: { children: ReactNode }) {
  return <ConvexProvider client={convex}>{children}</ConvexProvider>;
}
EOF

    print_success "Clerk setup completed"
}

# Update root layout
update_layout() {
    print_status "Updating root layout..."
    
    cat > src/app/layout.tsx << 'EOF'
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ClerkClientProvider } from "@/components/providers/clerk-provider";
import { ConvexClientProvider } from "@/components/providers/convex-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Next.js + Convex + Clerk App",
  description: "A modern full-stack application",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkClientProvider>
      <html lang="en">
        <body className={inter.className}>
          <ConvexClientProvider>
            {children}
          </ConvexClientProvider>
        </body>
      </html>
    </ClerkClientProvider>
  );
}
EOF

    print_success "Root layout updated"
}

# Create example pages
create_example_pages() {
    print_status "Creating example pages..."
    
    # Update home page
    cat > src/app/page.tsx << 'EOF'
import { SignInButton, SignOutButton, SignedIn, SignedOut, UserButton } from "@clerk/nextjs";

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm">
        <h1 className="text-4xl font-bold text-center mb-8">
          Next.js + Convex + Clerk
        </h1>
        
        <div className="text-center">
          <SignedOut>
            <div className="mb-4">
              <p className="mb-4">Please sign in to continue</p>
              <SignInButton mode="modal">
                <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                  Sign In
                </button>
              </SignInButton>
            </div>
          </SignedOut>
          
          <SignedIn>
            <div className="mb-4">
              <p className="mb-4">Welcome! You are signed in.</p>
              <div className="flex items-center justify-center gap-4">
                <UserButton afterSignOutUrl="/" />
                <SignOutButton>
                  <button className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Sign Out
                  </button>
                </SignOutButton>
              </div>
            </div>
          </SignedIn>
        </div>
        
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            This is a starter template with Next.js, Tailwind CSS, Convex, and Clerk
          </p>
        </div>
      </div>
    </main>
  );
}
EOF

    # Create sign-in page
    mkdir -p src/app/sign-in
    cat > src/app/sign-in/page.tsx << 'EOF'
import { SignIn } from "@clerk/nextjs";

export default function Page() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <SignIn />
    </div>
  );
}
EOF

    # Create sign-up page
    mkdir -p src/app/sign-up
    cat > src/app/sign-up/page.tsx << 'EOF'
import { SignUp } from "@clerk/nextjs";

export default function Page() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <SignUp />
    </div>
  );
}
EOF

    print_success "Example pages created"
}

# Create package.json scripts
update_package_scripts() {
    print_status "Updating package.json scripts..."
    
    # Add convex dev script
    npm pkg set scripts.dev="next dev & npx convex dev"
    npm pkg set scripts.convex="npx convex"
    
    print_success "Package scripts updated"
}

# Main installation function
main() {
    echo "🚀 Next.js + Tailwind + Convex + Clerk Setup Script"
    echo "=================================================="
    
    check_prerequisites
    get_project_name "$1"
    
    print_status "Starting setup for project: $PROJECT_NAME"
    
    create_nextjs_app
    install_dependencies
    setup_convex
    setup_clerk
    update_layout
    create_example_pages
    update_package_scripts
    
    echo ""
    print_success "🎉 Setup completed successfully!"
    echo ""
    print_warning "⚠️  Don't forget to:"
    echo "  1. Add your Clerk keys to .env.local"
    echo "  2. Configure your Convex deployment URL in .env.local"
    echo "  3. Update the Convex schema in convex/schema.ts"
    echo ""
    print_status "To start development:"
    echo "  cd $PROJECT_NAME"
    echo "  npm run dev"
    echo ""
    print_status "To deploy to Convex:"
    echo "  npx convex deploy"
}

# Run main function with all arguments
main "$@"